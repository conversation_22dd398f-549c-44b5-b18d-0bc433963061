#!/usr/bin/env python3
"""
Mürşid Giriş Uygulaması - Mürşid Login Application
"""

import flet as ft

def main(page: ft.Page):
    page.title = "<PERSON><PERSON>ş Yap - Mürşid"
    page.vertical_alignment = ft.MainAxisAlignment.CENTER
    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
    page.window_width = 350  # Varsayılan boyut
    page.window_height = 500  # Varsayılan boyut
    page.window_resizable = True  # Boyut değiştirilebilir
    page.window_min_width = 280  # Minimum genişlik
    page.window_min_height = 400  # Minimum yükseklik
    page.padding = 0  # Sayfa dolgusu (AppBar için sıfır)

    # Kullanıcı verilerini kaydetmek için değişkenler

    # Ayarlar menüsü fonksiyonları
    def show_about(e):
        page.snack_bar = ft.SnackBar(content=ft.Text("Mürşid v1.0 - Gü<PERSON>li Giriş Uygulaması"))
        page.snack_bar.open = True
        page.update()

    def show_help(e):
        page.snack_bar = ft.SnackBar(content=ft.Text("Yardım: E-posta ve şifrenizi girin"))
        page.snack_bar.open = True
        page.update()

    def show_settings(e):
        page.snack_bar = ft.SnackBar(content=ft.Text("Ayarlar sayfası yakında eklenecek"))
        page.snack_bar.open = True
        page.update()

    def show_language(e):
        page.snack_bar = ft.SnackBar(content=ft.Text("Dil seçenekleri: Türkçe, العربية, English"))
        page.snack_bar.open = True
        page.update()

    def show_theme(e):
        page.snack_bar = ft.SnackBar(content=ft.Text("Tema değiştirme yakında eklenecek"))
        page.snack_bar.open = True
        page.update()

    def show_privacy(e):
        page.snack_bar = ft.SnackBar(content=ft.Text("Gizlilik Politikası yakında eklenecek"))
        page.snack_bar.open = True
        page.update()

    def login_clicked(e):
        # Veri doğrulama
        if not email_field.value:
            email_field.error_text = "Lütfen e-posta adresinizi girin"
            page.update()
            return

        if not password_field.value:
            password_field.error_text = "Lütfen şifrenizi girin"
            page.update()
            return

        # Hata mesajlarını temizle
        email_field.error_text = ""
        password_field.error_text = ""

        # Başarılı giriş mesajı
        success_message.value = f"Hoş geldiniz! Giriş başarılı\nE-posta: {email_field.value}"
        success_message.visible = True
        login_button.disabled = True
        page.update()

    def register_clicked(e):
        # Kayıt mantığı buraya eklenebilir
        page.show_snack_bar(
            ft.SnackBar(content=ft.Text("Kayıt sayfası yakında eklenecek"))
        )

    def forgot_password_clicked(e):
        page.show_snack_bar(
            ft.SnackBar(content=ft.Text("Şifre sıfırlama yakında eklenecek"))
        )

    # Giriş alanları - responsive
    email_field = ft.TextField(
        label="E-posta",
        hint_text="<EMAIL>",
        expand=True,  # Mevcut alanı doldur
        prefix_icon=ft.Icons.EMAIL,
        keyboard_type=ft.KeyboardType.EMAIL,
        autofocus=True
    )

    password_field = ft.TextField(
        label="Şifre",
        hint_text="Şifrenizi girin",
        expand=True,  # Mevcut alanı doldur
        prefix_icon=ft.Icons.LOCK,
        password=True,
        can_reveal_password=True
    )

    # Uygulama düğmeleri - responsive
    login_button = ft.ElevatedButton(
        text="Giriş Yap",
        expand=True,  # Mevcut alanı doldur
        height=45,
        on_click=login_clicked,
        style=ft.ButtonStyle(
            color=ft.Colors.WHITE,
            bgcolor=ft.Colors.BLUE_600,
        )
    )

    register_button = ft.TextButton(
        text="Yeni hesap oluştur",
        on_click=register_clicked
    )

    forgot_password_button = ft.TextButton(
        text="Şifremi unuttum",
        on_click=forgot_password_clicked
    )

    # رسالة النجاح
    success_message = ft.Text(
        value="",
        color=ft.Colors.GREEN_600,
        size=16,
        text_align=ft.TextAlign.CENTER,
        visible=False
    )

    # AppBar ile ayarlar menüsü
    page.appbar = ft.AppBar(
        title=ft.Text("Mürşid", color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD),
        center_title=True,
        bgcolor=ft.Colors.BLUE_600,
        actions=[
            ft.PopupMenuButton(
                icon=ft.Icons.SETTINGS,
                icon_color=ft.Colors.WHITE,
                items=[
                    ft.PopupMenuItem(
                        text="Hakkında",
                        icon=ft.Icons.INFO,
                        on_click=show_about
                    ),
                    ft.PopupMenuItem(
                        text="Yardım",
                        icon=ft.Icons.HELP,
                        on_click=show_help
                    ),
                    ft.PopupMenuItem(
                        text="Ayarlar",
                        icon=ft.Icons.SETTINGS,
                        on_click=show_settings
                    ),
                    ft.PopupMenuItem(),  # Ayırıcı çizgi
                    ft.PopupMenuItem(
                        text="Dil",
                        icon=ft.Icons.LANGUAGE,
                        on_click=show_language
                    ),
                    ft.PopupMenuItem(
                        text="Tema",
                        icon=ft.Icons.PALETTE,
                        on_click=show_theme
                    ),
                    ft.PopupMenuItem(),  # Ayırıcı çizgi
                    ft.PopupMenuItem(
                        text="Gizlilik",
                        icon=ft.Icons.PRIVACY_TIP,
                        on_click=show_privacy
                    ),
                ]
            )
        ]
    )

    # تخطيط الصفحة - responsive
    page.add(
        ft.Container(
            content=ft.Column([
                # العنوان والشعار - responsive
                ft.Text(
                    "🛡️",
                    size=60,  # حجم كبير للوجو
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    "Mürşid",
                    size=24,  # حجم كبير لاسم التطبيق
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_600,
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    "Giriş Yap",
                    size=18,  # Alt başlık için küçük boyut
                    weight=ft.FontWeight.W_500,
                    color=ft.Colors.BLUE_700,
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    "Mürşid uygulamasına hoş geldiniz",
                    size=14,  # Orta boyut
                    color=ft.Colors.GREY_600,
                    text_align=ft.TextAlign.CENTER
                ),

                # مساحة فارغة
                ft.Container(height=10),  # مساحة صغيرة جداً

                # حقول الإدخال - responsive
                ft.ResponsiveRow([
                    ft.Column([
                        email_field,
                        ft.Container(height=8),
                        password_field,
                    ], col={"sm": 12, "md": 10, "lg": 8}, alignment=ft.MainAxisAlignment.CENTER)
                ], alignment=ft.MainAxisAlignment.CENTER),

                # مساحة فارغة
                ft.Container(height=10),  # مساحة صغيرة جداً

                # زر تسجيل الدخول - responsive
                ft.ResponsiveRow([
                    ft.Column([
                        login_button,
                    ], col={"sm": 12, "md": 10, "lg": 8}, alignment=ft.MainAxisAlignment.CENTER)
                ], alignment=ft.MainAxisAlignment.CENTER),

                # رسالة النجاح
                success_message,

                # مساحة فارغة
                ft.Container(height=8),  # مساحة أصغر للهاتف

                # أزرار إضافية
                register_button,
                forgot_password_button,

            ],
            alignment=ft.MainAxisAlignment.CENTER,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=8,  # مساحة متكيفة
            tight=True  # يتكيف مع المحتوى
            ),
            padding=ft.padding.symmetric(horizontal=20, vertical=20),  # حشو متكيف
            margin=ft.margin.only(left=10, right=10, top=10, bottom=10),  # هامش خارجي
            border_radius=10,
            bgcolor=ft.Colors.WHITE,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=15,
                color=ft.Colors.BLUE_GREY_300,
                offset=ft.Offset(0, 0),
            ),
            expand=True,  # يتوسع ليملأ المساحة المتاحة
            width=None,  # عرض متكيف
            height=None  # ارتفاع متكيف
        )
    )

if __name__ == "__main__":
    ft.app(target=main)
