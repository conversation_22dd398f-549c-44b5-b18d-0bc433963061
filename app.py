#!/usr/bin/env python3
"""
Flet Login Application - تطبيق تسجيل الدخول
"""

import flet as ft

def main(page: ft.Page):
    page.title = "تسجيل الدخول - Zem Login"
    page.vertical_alignment = ft.MainAxisAlignment.CENTER
    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
    page.window_width = 350  # حجم افتراضي
    page.window_height = 500  # حجم افتراضي
    page.window_resizable = True  # قابل لتغيير الحجم
    page.window_min_width = 280  # أقل عرض
    page.window_min_height = 400  # أقل ارتفاع
    page.padding = 10  # حشو للصفحة

    # متغيرات لحفظ بيانات المستخدم
    def login_clicked(e):
        # التحقق من صحة البيانات
        if not email_field.value:
            email_field.error_text = "يرجى إدخال الإيميل"
            page.update()
            return

        if not password_field.value:
            password_field.error_text = "يرجى إدخال كلمة المرور"
            page.update()
            return

        # مسح رسائل الخطأ
        email_field.error_text = ""
        password_field.error_text = ""

        # عرض رسالة نجاح تسجيل الدخول
        success_message.value = f"مرحباً! تم تسجيل الدخول بنجاح\nالإيميل: {email_field.value}"
        success_message.visible = True
        login_button.disabled = True
        page.update()

    def register_clicked(e):
        # يمكن إضافة منطق التسجيل هنا
        page.show_snack_bar(
            ft.SnackBar(content=ft.Text("سيتم إضافة صفحة التسجيل قريباً"))
        )

    def forgot_password_clicked(e):
        page.show_snack_bar(
            ft.SnackBar(content=ft.Text("سيتم إضافة استعادة كلمة المرور قريباً"))
        )

    # حقول الإدخال - responsive
    email_field = ft.TextField(
        label="الإيميل / Email",
        hint_text="<EMAIL>",
        expand=True,  # يتوسع ليملأ المساحة المتاحة
        prefix_icon=ft.Icons.EMAIL,
        keyboard_type=ft.KeyboardType.EMAIL,
        autofocus=True
    )

    password_field = ft.TextField(
        label="كلمة المرور / Password",
        hint_text="أدخل كلمة المرور",
        expand=True,  # يتوسع ليملأ المساحة المتاحة
        prefix_icon=ft.Icons.LOCK,
        password=True,
        can_reveal_password=True
    )

    # أزرار التطبيق - responsive
    login_button = ft.ElevatedButton(
        text="تسجيل الدخول",
        expand=True,  # يتوسع ليملأ المساحة المتاحة
        height=45,
        on_click=login_clicked,
        style=ft.ButtonStyle(
            color=ft.Colors.WHITE,
            bgcolor=ft.Colors.BLUE_600,
        )
    )

    register_button = ft.TextButton(
        text="إنشاء حساب جديد",
        on_click=register_clicked
    )

    forgot_password_button = ft.TextButton(
        text="نسيت كلمة المرور؟",
        on_click=forgot_password_clicked
    )

    # رسالة النجاح
    success_message = ft.Text(
        value="",
        color=ft.Colors.GREEN_600,
        size=16,
        text_align=ft.TextAlign.CENTER,
        visible=False
    )

    # تخطيط الصفحة - responsive
    page.add(
        ft.Container(
            content=ft.Column([
                # العنوان والشعار - responsive
                ft.Icon(
                    name=ft.Icons.ACCOUNT_CIRCLE,
                    size=50,  # حجم متوسط
                    color=ft.Colors.BLUE_600
                ),
                ft.Text(
                    "تسجيل الدخول",
                    size=22,  # حجم متوسط
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_600,
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    "مرحباً بك في تطبيق Zem",
                    size=14,  # حجم متوسط
                    color=ft.Colors.GREY_600,
                    text_align=ft.TextAlign.CENTER
                ),

                # مساحة فارغة
                ft.Container(height=10),  # مساحة صغيرة جداً

                # حقول الإدخال
                email_field,
                ft.Container(height=5),  # مساحة صغيرة جداً
                password_field,

                # مساحة فارغة
                ft.Container(height=10),  # مساحة صغيرة جداً

                # زر تسجيل الدخول
                login_button,

                # رسالة النجاح
                success_message,

                # مساحة فارغة
                ft.Container(height=8),  # مساحة أصغر للهاتف

                # أزرار إضافية
                register_button,
                forgot_password_button,

            ],
            alignment=ft.MainAxisAlignment.CENTER,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=8,  # مساحة متكيفة
            tight=True  # يتكيف مع المحتوى
            ),
            padding=ft.padding.symmetric(horizontal=20, vertical=15),  # حشو متكيف
            margin=ft.margin.all(10),  # هامش خارجي
            border_radius=10,
            bgcolor=ft.Colors.WHITE,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=15,
                color=ft.Colors.BLUE_GREY_300,
                offset=ft.Offset(0, 0),
            ),
            expand=True,  # يتوسع ليملأ المساحة المتاحة
            width=None,  # عرض متكيف
            height=None  # ارتفاع متكيف
        )
    )

if __name__ == "__main__":
    ft.app(target=main)
