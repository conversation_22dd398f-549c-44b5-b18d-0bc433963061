#!/usr/bin/env python3
"""
Mürşid Giriş Uygulaması - Mürşid Login Application
"""

import flet as ft

def main(page: ft.Page):
    page.title = "Giriş Yap - Mürşid"
    page.vertical_alignment = ft.MainAxisAlignment.CENTER
    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
    page.window_width = 350  # Varsayılan boyut
    page.window_height = 500  # Varsayılan boyut
    page.window_resizable = True  # Boyut değiştirilebilir
    page.window_min_width = 280  # Minimum genişlik
    page.window_min_height = 400  # Minimum yükseklik
    page.padding = 10  # Sayfa dolgusu

    # Kullanıcı verilerini kaydetmek için değişkenler
    current_language = "tr"  # Varsayılan dil: Türkçe

    # Dil metinleri
    texts = {
        "tr": {
            "title": "Giriş Yap - Mürşid",
            "login_title": "<PERSON><PERSON><PERSON> Ya<PERSON>",
            "welcome": "<PERSON><PERSON><PERSON>ş<PERSON> uygulamasına hoş geldiniz",
            "email_label": "E-posta",
            "email_hint": "<EMAIL>",
            "password_label": "Şifre",
            "password_hint": "Şifrenizi girin",
            "login_button": "Giriş Yap",
            "register_button": "Yeni hesap oluştur",
            "forgot_button": "Şifremi unuttum",
            "email_error": "Lütfen e-posta adresinizi girin",
            "password_error": "Lütfen şifrenizi girin",
            "success_message": "Hoş geldiniz! Giriş başarılı\nE-posta: ",
            "about": "Hakkında",
            "help": "Yardım",
            "settings": "Ayarlar",
            "language": "Dil",
            "theme": "Tema",
            "privacy": "Gizlilik",
            "about_text": "Mürşid v1.0 - Güvenli Giriş Uygulaması",
            "help_text": "Yardım: E-posta ve şifrenizi girin",
            "settings_text": "Ayarlar sayfası yakında eklenecek",
            "theme_text": "Tema değiştirme yakında eklenecek",
            "privacy_text": "Gizlilik Politikası yakında eklenecek",
            "register_text": "Kayıt sayfası yakında eklenecek",
            "forgot_text": "Şifre sıfırlama yakında eklenecek"
        },
        "ar": {
            "title": "تسجيل الدخول - Mürşid",
            "login_title": "تسجيل الدخول",
            "welcome": "مرحباً بك في تطبيق Mürşid",
            "email_label": "البريد الإلكتروني",
            "email_hint": "<EMAIL>",
            "password_label": "كلمة المرور",
            "password_hint": "أدخل كلمة المرور",
            "login_button": "تسجيل الدخول",
            "register_button": "إنشاء حساب جديد",
            "forgot_button": "نسيت كلمة المرور؟",
            "email_error": "يرجى إدخال البريد الإلكتروني",
            "password_error": "يرجى إدخال كلمة المرور",
            "success_message": "مرحباً! تم تسجيل الدخول بنجاح\nالبريد الإلكتروني: ",
            "about": "حول التطبيق",
            "help": "المساعدة",
            "settings": "الإعدادات",
            "language": "اللغة",
            "theme": "المظهر",
            "privacy": "الخصوصية",
            "about_text": "Mürşid الإصدار 1.0 - تطبيق دخول آمن",
            "help_text": "المساعدة: أدخل بريدك الإلكتروني وكلمة المرور",
            "settings_text": "صفحة الإعدادات ستضاف قريباً",
            "theme_text": "تغيير المظهر سيضاف قريباً",
            "privacy_text": "سياسة الخصوصية ستضاف قريباً",
            "register_text": "صفحة التسجيل ستضاف قريباً",
            "forgot_text": "استعادة كلمة المرور ستضاف قريباً"
        },
        "en": {
            "title": "Login - Mürşid",
            "login_title": "Login",
            "welcome": "Welcome to Mürşid application",
            "email_label": "Email",
            "email_hint": "<EMAIL>",
            "password_label": "Password",
            "password_hint": "Enter your password",
            "login_button": "Login",
            "register_button": "Create new account",
            "forgot_button": "Forgot password?",
            "email_error": "Please enter your email",
            "password_error": "Please enter your password",
            "success_message": "Welcome! Login successful\nEmail: ",
            "about": "About",
            "help": "Help",
            "settings": "Settings",
            "language": "Language",
            "theme": "Theme",
            "privacy": "Privacy",
            "about_text": "Mürşid v1.0 - Secure Login Application",
            "help_text": "Help: Enter your email and password",
            "settings_text": "Settings page coming soon",
            "theme_text": "Theme changing coming soon",
            "privacy_text": "Privacy Policy coming soon",
            "register_text": "Registration page coming soon",
            "forgot_text": "Password reset coming soon"
        }
    }

    # Sayfa başlığını ayarla
    page.title = texts[current_language]["title"]

    # Dil seçimi fonksiyonları
    def change_to_turkish(e):
        nonlocal current_language
        current_language = "tr"
        page.snack_bar = ft.SnackBar(content=ft.Text("Dil Türkçe olarak değiştirildi - Uygulama yeniden başlatılıyor..."))
        page.snack_bar.open = True
        page.update()
        # Sayfayı yeniden yükle
        page.clean()
        main(page)

    def change_to_arabic(e):
        nonlocal current_language
        current_language = "ar"
        page.snack_bar = ft.SnackBar(content=ft.Text("تم تغيير اللغة إلى العربية - جاري إعادة تشغيل التطبيق..."))
        page.snack_bar.open = True
        page.update()
        # Sayfayı yeniden yükle
        page.clean()
        main(page)

    def change_to_english(e):
        nonlocal current_language
        current_language = "en"
        page.snack_bar = ft.SnackBar(content=ft.Text("Language changed to English - Restarting application..."))
        page.snack_bar.open = True
        page.update()
        # Sayfayı yeniden yükle
        page.clean()
        main(page)

    # Ayarlar menüsü fonksiyonları
    def show_about(e):
        page.snack_bar = ft.SnackBar(content=ft.Text(texts[current_language]["about_text"]))
        page.snack_bar.open = True
        page.update()

    def show_help(e):
        page.snack_bar = ft.SnackBar(content=ft.Text(texts[current_language]["help_text"]))
        page.snack_bar.open = True
        page.update()

    def show_settings(e):
        page.snack_bar = ft.SnackBar(content=ft.Text(texts[current_language]["settings_text"]))
        page.snack_bar.open = True
        page.update()

    def show_language_menu(e):
        # Dil seçimi menüsü göster
        page.snack_bar = ft.SnackBar(
            content=ft.Row([
                ft.TextButton("🇹🇷 Türkçe", on_click=change_to_turkish),
                ft.TextButton("🇸🇦 العربية", on_click=change_to_arabic),
                ft.TextButton("🇺🇸 English", on_click=change_to_english),
            ], alignment=ft.MainAxisAlignment.CENTER),
            action="Dil Seçin / اختر اللغة / Choose Language"
        )
        page.snack_bar.open = True
        page.update()

    def show_theme(e):
        page.snack_bar = ft.SnackBar(content=ft.Text(texts[current_language]["theme_text"]))
        page.snack_bar.open = True
        page.update()

    def show_privacy(e):
        page.snack_bar = ft.SnackBar(content=ft.Text(texts[current_language]["privacy_text"]))
        page.snack_bar.open = True
        page.update()

    def login_clicked(e):
        # Veri doğrulama
        if not email_field.value:
            email_field.error_text = texts[current_language]["email_error"]
            page.update()
            return

        if not password_field.value:
            password_field.error_text = texts[current_language]["password_error"]
            page.update()
            return

        # Hata mesajlarını temizle
        email_field.error_text = ""
        password_field.error_text = ""

        # Başarılı giriş mesajı
        success_message.value = f"{texts[current_language]['success_message']}{email_field.value}"
        success_message.visible = True
        login_button.disabled = True
        page.update()

    def register_clicked(e):
        # Kayıt mantığı buraya eklenebilir
        page.snack_bar = ft.SnackBar(content=ft.Text(texts[current_language]["register_text"]))
        page.snack_bar.open = True
        page.update()

    def forgot_password_clicked(e):
        page.snack_bar = ft.SnackBar(content=ft.Text(texts[current_language]["forgot_text"]))
        page.snack_bar.open = True
        page.update()

    # Giriş alanları - responsive
    email_field = ft.TextField(
        label=texts[current_language]["email_label"],
        hint_text=texts[current_language]["email_hint"],
        expand=True,  # Mevcut alanı doldur
        prefix_icon=ft.Icons.EMAIL,
        keyboard_type=ft.KeyboardType.EMAIL,
        autofocus=True
    )

    password_field = ft.TextField(
        label=texts[current_language]["password_label"],
        hint_text=texts[current_language]["password_hint"],
        expand=True,  # Mevcut alanı doldur
        prefix_icon=ft.Icons.LOCK,
        password=True,
        can_reveal_password=True
    )

    # Uygulama düğmeleri - responsive
    login_button = ft.ElevatedButton(
        text=texts[current_language]["login_button"],
        expand=True,  # Mevcut alanı doldur
        height=45,
        on_click=login_clicked,
        style=ft.ButtonStyle(
            color=ft.Colors.WHITE,
            bgcolor=ft.Colors.BLUE_600,
        )
    )

    register_button = ft.TextButton(
        text=texts[current_language]["register_button"],
        on_click=register_clicked
    )

    forgot_password_button = ft.TextButton(
        text=texts[current_language]["forgot_button"],
        on_click=forgot_password_clicked
    )

    # رسالة النجاح
    success_message = ft.Text(
        value="",
        color=ft.Colors.GREEN_600,
        size=16,
        text_align=ft.TextAlign.CENTER,
        visible=False
    )



    # تخطيط الصفحة - responsive
    page.add(
        ft.Container(
            content=ft.Column([
                # قائمة الإعدادات في الأعلى
                ft.Row([
                    ft.Container(expand=True),  # مساحة فارغة لدفع القائمة لليمين
                    ft.PopupMenuButton(
                        icon=ft.Icons.MORE_VERT,  # أيقونة الثلاث نقاط
                        items=[
                            ft.PopupMenuItem(
                                text=texts[current_language]["about"],
                                icon=ft.Icons.INFO,
                                on_click=show_about
                            ),
                            ft.PopupMenuItem(
                                text=texts[current_language]["help"],
                                icon=ft.Icons.HELP,
                                on_click=show_help
                            ),
                            ft.PopupMenuItem(
                                text=texts[current_language]["settings"],
                                icon=ft.Icons.SETTINGS,
                                on_click=show_settings
                            ),
                            ft.PopupMenuItem(),  # Ayırıcı çizgi
                            ft.PopupMenuItem(
                                text=texts[current_language]["language"],
                                icon=ft.Icons.LANGUAGE,
                                on_click=show_language_menu
                            ),
                            ft.PopupMenuItem(
                                text=texts[current_language]["theme"],
                                icon=ft.Icons.PALETTE,
                                on_click=show_theme
                            ),
                            ft.PopupMenuItem(),  # Ayırıcı çizgi
                            ft.PopupMenuItem(
                                text=texts[current_language]["privacy"],
                                icon=ft.Icons.PRIVACY_TIP,
                                on_click=show_privacy
                            ),
                        ]
                    )
                ], alignment=ft.MainAxisAlignment.END),

                # العنوان والشعار - responsive
                ft.Text(
                    "🛡️",
                    size=60,  # حجم كبير للوجو
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    "Mürşid",
                    size=24,  # حجم كبير لاسم التطبيق
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_600,
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    texts[current_language]["login_title"],
                    size=18,  # Alt başlık için küçük boyut
                    weight=ft.FontWeight.W_500,
                    color=ft.Colors.BLUE_700,
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    texts[current_language]["welcome"],
                    size=14,  # Orta boyut
                    color=ft.Colors.GREY_600,
                    text_align=ft.TextAlign.CENTER
                ),

                # مساحة فارغة
                ft.Container(height=10),  # مساحة صغيرة جداً

                # حقول الإدخال - responsive
                ft.ResponsiveRow([
                    ft.Column([
                        email_field,
                        ft.Container(height=8),
                        password_field,
                    ], col={"sm": 12, "md": 10, "lg": 8}, alignment=ft.MainAxisAlignment.CENTER)
                ], alignment=ft.MainAxisAlignment.CENTER),

                # مساحة فارغة
                ft.Container(height=10),  # مساحة صغيرة جداً

                # زر تسجيل الدخول - responsive
                ft.ResponsiveRow([
                    ft.Column([
                        login_button,
                    ], col={"sm": 12, "md": 10, "lg": 8}, alignment=ft.MainAxisAlignment.CENTER)
                ], alignment=ft.MainAxisAlignment.CENTER),

                # رسالة النجاح
                success_message,

                # مساحة فارغة
                ft.Container(height=8),  # مساحة أصغر للهاتف

                # أزرار إضافية
                register_button,
                forgot_password_button,

            ],
            alignment=ft.MainAxisAlignment.CENTER,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=8,  # مساحة متكيفة
            tight=True  # يتكيف مع المحتوى
            ),
            padding=ft.padding.symmetric(horizontal=20, vertical=20),  # حشو متكيف
            margin=ft.margin.only(left=10, right=10, top=10, bottom=10),  # هامش خارجي
            border_radius=10,
            bgcolor=ft.Colors.WHITE,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=15,
                color=ft.Colors.BLUE_GREY_300,
                offset=ft.Offset(0, 0),
            ),
            expand=True,  # يتوسع ليملأ المساحة المتاحة
            width=None,  # عرض متكيف
            height=None  # ارتفاع متكيف
        )
    )

if __name__ == "__main__":
    ft.app(target=main)
