#!/usr/bin/env python3
"""
Mürşid Login Application - تطبيق تسجيل الدخول Mürşid
"""

import flet as ft

def main(page: ft.Page):
    page.title = "<PERSON><PERSON><PERSON>ürşid"
    page.vertical_alignment = ft.MainAxisAlignment.CENTER
    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
    page.window_width = 350  # حجم افتراضي
    page.window_height = 500  # حجم افتراضي
    page.window_resizable = True  # قابل لتغيير الحجم
    page.window_min_width = 280  # أقل عرض
    page.window_min_height = 400  # أقل ارتفاع
    page.padding = 10  # حشو للصفحة

    # متغيرات لحفظ بيانات المستخدم
    def login_clicked(e):
        # Veri doğrulama
        if not email_field.value:
            email_field.error_text = "Lütfen e-posta adresinizi girin"
            page.update()
            return

        if not password_field.value:
            password_field.error_text = "Lütfen şifrenizi girin"
            page.update()
            return

        # Hata mesajlarını temizle
        email_field.error_text = ""
        password_field.error_text = ""

        # Başarılı giriş mesajı
        success_message.value = f"Hoş geldiniz! Giriş başarılı\nE-posta: {email_field.value}"
        success_message.visible = True
        login_button.disabled = True
        page.update()

    def register_clicked(e):
        # Kayıt mantığı buraya eklenebilir
        page.show_snack_bar(
            ft.SnackBar(content=ft.Text("Kayıt sayfası yakında eklenecek"))
        )

    def forgot_password_clicked(e):
        page.show_snack_bar(
            ft.SnackBar(content=ft.Text("Şifre sıfırlama yakında eklenecek"))
        )

    # Giriş alanları - responsive
    email_field = ft.TextField(
        label="E-posta",
        hint_text="<EMAIL>",
        expand=True,  # Mevcut alanı doldur
        prefix_icon=ft.Icons.EMAIL,
        keyboard_type=ft.KeyboardType.EMAIL,
        autofocus=True
    )

    password_field = ft.TextField(
        label="Şifre",
        hint_text="Şifrenizi girin",
        expand=True,  # Mevcut alanı doldur
        prefix_icon=ft.Icons.LOCK,
        password=True,
        can_reveal_password=True
    )

    # Uygulama düğmeleri - responsive
    login_button = ft.ElevatedButton(
        text="Giriş Yap",
        expand=True,  # Mevcut alanı doldur
        height=45,
        on_click=login_clicked,
        style=ft.ButtonStyle(
            color=ft.Colors.WHITE,
            bgcolor=ft.Colors.BLUE_600,
        )
    )

    register_button = ft.TextButton(
        text="Yeni hesap oluştur",
        on_click=register_clicked
    )

    forgot_password_button = ft.TextButton(
        text="Şifremi unuttum",
        on_click=forgot_password_clicked
    )

    # رسالة النجاح
    success_message = ft.Text(
        value="",
        color=ft.Colors.GREEN_600,
        size=16,
        text_align=ft.TextAlign.CENTER,
        visible=False
    )

    # تخطيط الصفحة - responsive
    page.add(
        ft.Container(
            content=ft.Column([
                # العنوان والشعار - responsive
                ft.Text(
                    "🛡️",
                    size=60,  # حجم كبير للوجو
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    "Mürşid",
                    size=24,  # حجم كبير لاسم التطبيق
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_600,
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    "Giriş Yap",
                    size=18,  # Alt başlık için küçük boyut
                    weight=ft.FontWeight.W_500,
                    color=ft.Colors.BLUE_700,
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    "Mürşid uygulamasına hoş geldiniz",
                    size=14,  # Orta boyut
                    color=ft.Colors.GREY_600,
                    text_align=ft.TextAlign.CENTER
                ),

                # مساحة فارغة
                ft.Container(height=10),  # مساحة صغيرة جداً

                # حقول الإدخال - responsive
                ft.ResponsiveRow([
                    ft.Column([
                        email_field,
                        ft.Container(height=8),
                        password_field,
                    ], col={"sm": 12, "md": 10, "lg": 8}, alignment=ft.MainAxisAlignment.CENTER)
                ], alignment=ft.MainAxisAlignment.CENTER),

                # مساحة فارغة
                ft.Container(height=10),  # مساحة صغيرة جداً

                # زر تسجيل الدخول - responsive
                ft.ResponsiveRow([
                    ft.Column([
                        login_button,
                    ], col={"sm": 12, "md": 10, "lg": 8}, alignment=ft.MainAxisAlignment.CENTER)
                ], alignment=ft.MainAxisAlignment.CENTER),

                # رسالة النجاح
                success_message,

                # مساحة فارغة
                ft.Container(height=8),  # مساحة أصغر للهاتف

                # أزرار إضافية
                register_button,
                forgot_password_button,

            ],
            alignment=ft.MainAxisAlignment.CENTER,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=8,  # مساحة متكيفة
            tight=True  # يتكيف مع المحتوى
            ),
            padding=ft.padding.symmetric(horizontal=20, vertical=15),  # حشو متكيف
            margin=ft.margin.all(10),  # هامش خارجي
            border_radius=10,
            bgcolor=ft.Colors.WHITE,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=15,
                color=ft.Colors.BLUE_GREY_300,
                offset=ft.Offset(0, 0),
            ),
            expand=True,  # يتوسع ليملأ المساحة المتاحة
            width=None,  # عرض متكيف
            height=None  # ارتفاع متكيف
        )
    )

if __name__ == "__main__":
    ft.app(target=main)
