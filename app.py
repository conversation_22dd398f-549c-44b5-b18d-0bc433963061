#!/usr/bin/env python3
"""
Mürşid Giriş Uygulaması - Mürşid Login Application
"""

import flet as ft

def main(page: ft.Page):
    page.title = "<PERSON>iriş Yap - Mürşid"
    page.vertical_alignment = ft.MainAxisAlignment.CENTER
    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
    page.window_width = 350  # Varsayılan boyut
    page.window_height = 500  # Varsayılan boyut
    page.window_resizable = True  # Boyut değiştirilebilir
    page.window_min_width = 280  # Minimum genişlik
    page.window_min_height = 400  # Minimum yükseklik
    page.padding = 10  # Sayfa dolgusu

    # Kullanıcı verilerini kaydetmek için deği<PERSON>kenler
    if not hasattr(page, 'current_language'):
        page.current_language = "tr"  # Varsayılan dil: Türkçe
    current_language = page.current_language

    # Dil metinleri
    texts = {
        "tr": {
            "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            "login_title": "<PERSON><PERSON><PERSON> Ya<PERSON>",
            "welcome": "<PERSON><PERSON><PERSON><PERSON><PERSON> uygulamasına ho<PERSON> geldiniz",
            "email_label": "E-posta",
            "email_hint": "<EMAIL>",
            "password_label": "Şifre",
            "password_hint": "Şifrenizi girin",
            "login_button": "Giriş Yap",
            "register_button": "Yeni hesap oluştur",
            "forgot_button": "Şifremi unuttum",
            "email_error": "Lütfen e-posta adresinizi girin",
            "password_error": "Lütfen şifrenizi girin",
            "success_message": "Hoş geldiniz! Giriş başarılı\nE-posta: ",
            "about": "Hakkında",
            "help": "Yardım",
            "settings": "Ayarlar",
            "language": "Dil",
            "theme": "Tema",
            "privacy": "Gizlilik",
            "about_text": "Mürşid v1.0 - Güvenli Giriş Uygulaması",
            "help_text": "Yardım: E-posta ve şifrenizi girin",
            "settings_text": "Ayarlar sayfası yakında eklenecek",
            "theme_text": "Tema değiştirme yakında eklenecek",
            "privacy_text": "Gizlilik Politikası yakında eklenecek",
            "register_text": "Kayıt sayfası yakında eklenecek",
            "forgot_text": "Şifre sıfırlama yakında eklenecek",
            "language_name": "Türkçe"
        },
        "ar": {
            "title": "تسجيل الدخول - Mürşid",
            "login_title": "تسجيل الدخول",
            "welcome": "مرحباً بك في تطبيق Mürşid",
            "email_label": "البريد الإلكتروني",
            "email_hint": "<EMAIL>",
            "password_label": "كلمة المرور",
            "password_hint": "أدخل كلمة المرور",
            "login_button": "تسجيل الدخول",
            "register_button": "إنشاء حساب جديد",
            "forgot_button": "نسيت كلمة المرور؟",
            "email_error": "يرجى إدخال البريد الإلكتروني",
            "password_error": "يرجى إدخال كلمة المرور",
            "success_message": "مرحباً! تم تسجيل الدخول بنجاح\nالبريد الإلكتروني: ",
            "about": "حول التطبيق",
            "help": "المساعدة",
            "settings": "الإعدادات",
            "language": "اللغة",
            "theme": "المظهر",
            "privacy": "الخصوصية",
            "about_text": "Mürşid الإصدار 1.0 - تطبيق دخول آمن",
            "help_text": "المساعدة: أدخل بريدك الإلكتروني وكلمة المرور",
            "settings_text": "صفحة الإعدادات ستضاف قريباً",
            "theme_text": "تغيير المظهر سيضاف قريباً",
            "privacy_text": "سياسة الخصوصية ستضاف قريباً",
            "register_text": "صفحة التسجيل ستضاف قريباً",
            "forgot_text": "استعادة كلمة المرور ستضاف قريباً",
            "language_name": "العربية"
        },
        "en": {
            "title": "Login - Mürşid",
            "login_title": "Login",
            "welcome": "Welcome to Mürşid application",
            "email_label": "Email",
            "email_hint": "<EMAIL>",
            "password_label": "Password",
            "password_hint": "Enter your password",
            "login_button": "Login",
            "register_button": "Create new account",
            "forgot_button": "Forgot password?",
            "email_error": "Please enter your email",
            "password_error": "Please enter your password",
            "success_message": "Welcome! Login successful\nEmail: ",
            "about": "About",
            "help": "Help",
            "settings": "Settings",
            "language": "Language",
            "theme": "Theme",
            "privacy": "Privacy",
            "about_text": "Mürşid v1.0 - Secure Login Application",
            "help_text": "Help: Enter your email and password",
            "settings_text": "Settings page coming soon",
            "theme_text": "Theme changing coming soon",
            "privacy_text": "Privacy Policy coming soon",
            "register_text": "Registration page coming soon",
            "forgot_text": "Password reset coming soon"
        }
    }

    # Sayfa başlığını ayarla
    page.title = texts[current_language]["title"]

    # Dil seçimi fonksiyonları
    def change_to_turkish(e):
        if hasattr(page, 'dialog') and page.dialog:
            page.dialog.open = False
        page.current_language = "tr"
        page.snack_bar = ft.SnackBar(content=ft.Text("Dil Türkçe olarak değiştirildi"))
        page.snack_bar.open = True
        page.update()
        # Sayfayı yeniden yükle
        import time
        time.sleep(0.5)
        page.clean()
        main(page)

    def change_to_arabic(e):
        if hasattr(page, 'dialog') and page.dialog:
            page.dialog.open = False
        page.current_language = "ar"
        page.snack_bar = ft.SnackBar(content=ft.Text("تم تغيير اللغة إلى العربية"))
        page.snack_bar.open = True
        page.update()
        # Sayfayı yeniden yükle
        import time
        time.sleep(0.5)
        page.clean()
        main(page)

    def change_to_english(e):
        if hasattr(page, 'dialog') and page.dialog:
            page.dialog.open = False
        page.current_language = "en"
        page.snack_bar = ft.SnackBar(content=ft.Text("Language changed to English"))
        page.snack_bar.open = True
        page.update()
        # Sayfayı yeniden yükle
        import time
        time.sleep(0.5)
        page.clean()
        main(page)

    # Ayarlar menüsü fonksiyonları
    def show_about(e):
        page.snack_bar = ft.SnackBar(content=ft.Text(texts[current_language]["about_text"]))
        page.snack_bar.open = True
        page.update()

    def show_help(e):
        page.snack_bar = ft.SnackBar(content=ft.Text(texts[current_language]["help_text"]))
        page.snack_bar.open = True
        page.update()

    def show_settings(e):
        print("Settings clicked!")  # للتحقق

        # استخدام صفحة جديدة بدلاً من BottomSheet
        def close_settings():
            page.clean()
            main(page)

        def toggle_notifications(e):
            status = "ON" if e.control.value else "OFF"
            page.snack_bar = ft.SnackBar(content=ft.Text(f"Notifications: {status}"))
            page.snack_bar.open = True
            page.update()

        def toggle_auto_login(e):
            status = "ON" if e.control.value else "OFF"
            page.snack_bar = ft.SnackBar(content=ft.Text(f"Auto Login: {status}"))
            page.snack_bar.open = True
            page.update()

        # مسح الصفحة الحالية وإظهار صفحة الإعدادات
        page.clean()

        page.add(
            ft.Container(
                content=ft.Column([
                    # شريط علوي مع زر الرجوع
                    ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.ARROW_BACK,
                            on_click=lambda e: close_settings()
                        ),
                        ft.Text(
                            "Ayarlar" if current_language == "tr"
                            else "الإعدادات" if current_language == "ar"
                            else "Settings",
                            size=20, weight=ft.FontWeight.BOLD
                        ),
                        ft.Container(expand=True),
                    ]),

                    ft.Divider(),

                    # الإعدادات
                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.NOTIFICATIONS, color=ft.Colors.BLUE),
                        title=ft.Text(
                            "Bildirimler" if current_language == "tr"
                            else "الإشعارات" if current_language == "ar"
                            else "Notifications"
                        ),
                        subtitle=ft.Text(
                            "Bildirimleri aç/kapat" if current_language == "tr"
                            else "تشغيل/إيقاف الإشعارات" if current_language == "ar"
                            else "Turn notifications on/off"
                        ),
                        trailing=ft.Switch(value=True, on_change=toggle_notifications)
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.LOGIN, color=ft.Colors.GREEN),
                        title=ft.Text(
                            "Otomatik Giriş" if current_language == "tr"
                            else "الدخول التلقائي" if current_language == "ar"
                            else "Auto Login"
                        ),
                        subtitle=ft.Text(
                            "Otomatik giriş yap" if current_language == "tr"
                            else "تسجيل دخول تلقائي" if current_language == "ar"
                            else "Automatic login"
                        ),
                        trailing=ft.Switch(value=False, on_change=toggle_auto_login)
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.SECURITY, color=ft.Colors.ORANGE),
                        title=ft.Text(
                            "Güvenlik" if current_language == "tr"
                            else "الأمان" if current_language == "ar"
                            else "Security"
                        ),
                        subtitle=ft.Text(
                            "Güvenlik ayarları" if current_language == "tr"
                            else "إعدادات الأمان" if current_language == "ar"
                            else "Security settings"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=lambda e: show_security_settings()
                    ),

                    ft.Container(height=30),

                    # زر الرجوع
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.ARROW_BACK),
                            ft.Text(
                                "Geri Dön" if current_language == "tr"
                                else "العودة" if current_language == "ar"
                                else "Go Back"
                            )
                        ], alignment=ft.MainAxisAlignment.CENTER),
                        width=200,
                        on_click=lambda e: close_settings()
                    )

                ], spacing=10, scroll=ft.ScrollMode.AUTO),
                padding=20,
                expand=True
            )
        )

    def show_security_settings():
        print("Security settings clicked!")  # للتحقق

        # استخدام صفحة جديدة لإعدادات الأمان
        def close_security():
            page.clean()
            main(page)

        def toggle_two_factor(e):
            status = "تشغيل" if e.control.value else "إيقاف"
            page.snack_bar = ft.SnackBar(content=ft.Text(
                f"İki faktörlü kimlik doğrulama: {'Açık' if e.control.value else 'Kapalı'}" if current_language == "tr"
                else f"المصادقة الثنائية: {status}" if current_language == "ar"
                else f"Two-factor authentication: {'ON' if e.control.value else 'OFF'}"
            ))
            page.snack_bar.open = True
            page.update()

        def toggle_biometric(e):
            status = "تشغيل" if e.control.value else "إيقاف"
            page.snack_bar = ft.SnackBar(content=ft.Text(
                f"Biyometrik giriş: {'Açık' if e.control.value else 'Kapalı'}" if current_language == "tr"
                else f"الدخول البيومتري: {status}" if current_language == "ar"
                else f"Biometric login: {'ON' if e.control.value else 'OFF'}"
            ))
            page.snack_bar.open = True
            page.update()

        def toggle_session_timeout(e):
            status = "تشغيل" if e.control.value else "إيقاف"
            page.snack_bar = ft.SnackBar(content=ft.Text(
                f"Oturum zaman aşımı: {'Açık' if e.control.value else 'Kapalı'}" if current_language == "tr"
                else f"انتهاء مهلة الجلسة: {status}" if current_language == "ar"
                else f"Session timeout: {'ON' if e.control.value else 'OFF'}"
            ))
            page.snack_bar.open = True
            page.update()

        def show_login_history(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Giriş geçmişi gösteriliyor..." if current_language == "tr"
                else "عرض سجل تسجيل الدخول..." if current_language == "ar"
                else "Showing login history..."
            ))
            page.snack_bar.open = True
            page.update()

        def change_password(e):
            show_change_password_page()

        def logout_all_devices(e):
            show_logout_confirmation()

        def show_privacy_settings(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Gizlilik ayarları açılıyor..." if current_language == "tr"
                else "فتح إعدادات الخصوصية..." if current_language == "ar"
                else "Opening privacy settings..."
            ))
            page.snack_bar.open = True
            page.update()

        def show_device_management(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Cihaz yönetimi açılıyor..." if current_language == "tr"
                else "فتح إدارة الأجهزة..." if current_language == "ar"
                else "Opening device management..."
            ))
            page.snack_bar.open = True
            page.update()

        def show_change_password_page():
            # صفحة تغيير كلمة المرور
            def close_password_page():
                page.clean()
                show_security_settings()

            def save_new_password(e):
                if new_password.value and confirm_password.value:
                    if new_password.value == confirm_password.value:
                        page.snack_bar = ft.SnackBar(content=ft.Text(
                            "Şifre başarıyla değiştirildi!" if current_language == "tr"
                            else "تم تغيير كلمة المرور بنجاح!" if current_language == "ar"
                            else "Password changed successfully!"
                        ))
                        page.snack_bar.open = True
                        page.update()
                        import time
                        import threading
                        def go_back():
                            time.sleep(2)
                            close_password_page()
                        threading.Thread(target=go_back).start()
                    else:
                        page.snack_bar = ft.SnackBar(content=ft.Text(
                            "Şifreler eşleşmiyor!" if current_language == "tr"
                            else "كلمات المرور غير متطابقة!" if current_language == "ar"
                            else "Passwords don't match!"
                        ))
                        page.snack_bar.open = True
                        page.update()
                else:
                    page.snack_bar = ft.SnackBar(content=ft.Text(
                        "Lütfen tüm alanları doldurun!" if current_language == "tr"
                        else "يرجى ملء جميع الحقول!" if current_language == "ar"
                        else "Please fill all fields!"
                    ))
                    page.snack_bar.open = True
                    page.update()

            page.clean()

            current_password = ft.TextField(
                label="Mevcut Şifre" if current_language == "tr"
                else "كلمة المرور الحالية" if current_language == "ar"
                else "Current Password",
                password=True,
                expand=True
            )

            new_password = ft.TextField(
                label="Yeni Şifre" if current_language == "tr"
                else "كلمة المرور الجديدة" if current_language == "ar"
                else "New Password",
                password=True,
                expand=True
            )

            confirm_password = ft.TextField(
                label="Yeni Şifre Tekrar" if current_language == "tr"
                else "تأكيد كلمة المرور الجديدة" if current_language == "ar"
                else "Confirm New Password",
                password=True,
                expand=True
            )

            page.add(
                ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.IconButton(icon=ft.Icons.ARROW_BACK, on_click=lambda e: close_password_page()),
                            ft.Text(
                                "Şifre Değiştir" if current_language == "tr"
                                else "تغيير كلمة المرور" if current_language == "ar"
                                else "Change Password",
                                size=20, weight=ft.FontWeight.BOLD
                            ),
                            ft.Container(expand=True),
                        ]),

                        ft.Divider(),

                        ft.Icon(ft.Icons.LOCK_RESET, size=60, color=ft.Colors.BLUE_600),

                        ft.Text(
                            "Güvenliğiniz için güçlü bir şifre seçin" if current_language == "tr"
                            else "اختر كلمة مرور قوية لأمانك" if current_language == "ar"
                            else "Choose a strong password for your security",
                            text_align=ft.TextAlign.CENTER,
                            size=14,
                            color=ft.Colors.GREY_600
                        ),

                        ft.Container(height=20),

                        current_password,
                        ft.Container(height=10),
                        new_password,
                        ft.Container(height=10),
                        confirm_password,

                        ft.Container(height=20),

                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.Icons.SAVE),
                                ft.Text(
                                    "Şifreyi Kaydet" if current_language == "tr"
                                    else "حفظ كلمة المرور" if current_language == "ar"
                                    else "Save Password"
                                )
                            ], alignment=ft.MainAxisAlignment.CENTER),
                            width=250,
                            on_click=save_new_password,
                            style=ft.ButtonStyle(bgcolor=ft.Colors.GREEN_600, color=ft.Colors.WHITE)
                        ),

                        ft.Container(height=10),

                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.Icons.CANCEL),
                                ft.Text(
                                    "İptal" if current_language == "tr"
                                    else "إلغاء" if current_language == "ar"
                                    else "Cancel"
                                )
                            ], alignment=ft.MainAxisAlignment.CENTER),
                            width=250,
                            on_click=lambda e: close_password_page()
                        )

                    ], alignment=ft.MainAxisAlignment.CENTER, spacing=10, scroll=ft.ScrollMode.AUTO),
                    padding=20,
                    expand=True
                )
            )

        def show_logout_confirmation():
            # نافذة تأكيد تسجيل الخروج
            def close_dialog(e):
                dialog.open = False
                page.update()

            def confirm_logout(e):
                page.snack_bar = ft.SnackBar(content=ft.Text(
                    "Tüm cihazlardan çıkış yapıldı!" if current_language == "tr"
                    else "تم تسجيل الخروج من جميع الأجهزة!" if current_language == "ar"
                    else "Logged out from all devices!"
                ))
                page.snack_bar.open = True
                page.update()
                dialog.open = False
                page.update()

            dialog = ft.AlertDialog(
                title=ft.Text(
                    "Tüm Cihazlardan Çıkış" if current_language == "tr"
                    else "تسجيل الخروج من جميع الأجهزة" if current_language == "ar"
                    else "Logout from All Devices"
                ),
                content=ft.Text(
                    "Bu işlem tüm cihazlardaki oturumlarınızı sonlandıracak. Devam etmek istiyor musunuz?" if current_language == "tr"
                    else "هذا الإجراء سينهي جلساتك على جميع الأجهزة. هل تريد المتابعة؟" if current_language == "ar"
                    else "This action will end your sessions on all devices. Do you want to continue?"
                ),
                actions=[
                    ft.TextButton(
                        "İptal" if current_language == "tr"
                        else "إلغاء" if current_language == "ar"
                        else "Cancel",
                        on_click=close_dialog
                    ),
                    ft.ElevatedButton(
                        "Çıkış Yap" if current_language == "tr"
                        else "تسجيل الخروج" if current_language == "ar"
                        else "Logout",
                        on_click=confirm_logout,
                        style=ft.ButtonStyle(bgcolor=ft.Colors.RED_600, color=ft.Colors.WHITE)
                    )
                ]
            )

            page.dialog = dialog
            dialog.open = True
            page.update()

        def show_backup_message():
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Yedekleme özelliği yakında..." if current_language == "tr"
                else "ميزة النسخ الاحتياطي قريباً..." if current_language == "ar"
                else "Backup feature coming soon..."
            ))
            page.snack_bar.open = True
            page.update()

        # مسح الصفحة الحالية وإظهار صفحة الأمان
        page.clean()

        page.add(
            ft.Container(
                content=ft.Column([
                    # شريط علوي مع زر الرجوع
                    ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.ARROW_BACK,
                            on_click=lambda e: close_security()
                        ),
                        ft.Text(
                            "Güvenlik Ayarları" if current_language == "tr"
                            else "إعدادات الأمان" if current_language == "ar"
                            else "Security Settings",
                            size=20, weight=ft.FontWeight.BOLD
                        ),
                        ft.Container(expand=True),
                    ]),

                    ft.Divider(),

                    # مؤشر حالة الأمان
                    ft.Container(
                        content=ft.Row([
                            ft.Icon(ft.Icons.SHIELD, color=ft.Colors.GREEN, size=30),
                            ft.Column([
                                ft.Text(
                                    "Güvenlik Durumu: İyi" if current_language == "tr"
                                    else "حالة الأمان: جيدة" if current_language == "ar"
                                    else "Security Status: Good",
                                    weight=ft.FontWeight.BOLD,
                                    color=ft.Colors.GREEN_700
                                ),
                                ft.Text(
                                    "Hesabınız güvende" if current_language == "tr"
                                    else "حسابك آمن" if current_language == "ar"
                                    else "Your account is secure",
                                    size=12,
                                    color=ft.Colors.GREY_600
                                )
                            ], spacing=2, expand=True),
                            ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN)
                        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                        padding=15,
                        bgcolor=ft.Colors.GREEN_50,
                        border_radius=10,
                        border=ft.border.all(1, ft.Colors.GREEN_200)
                    ),

                    ft.Container(height=15),

                    # قسم المصادقة
                    ft.Text(
                        "Kimlik Doğrulama" if current_language == "tr"
                        else "المصادقة" if current_language == "ar"
                        else "Authentication",
                        size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.SECURITY, color=ft.Colors.RED),
                        title=ft.Text(
                            "İki Faktörlü Kimlik Doğrulama" if current_language == "tr"
                            else "المصادقة الثنائية" if current_language == "ar"
                            else "Two-Factor Authentication"
                        ),
                        subtitle=ft.Text(
                            "Ekstra güvenlik katmanı" if current_language == "tr"
                            else "طبقة أمان إضافية" if current_language == "ar"
                            else "Extra security layer"
                        ),
                        trailing=ft.Switch(value=False, on_change=toggle_two_factor)
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.FINGERPRINT, color=ft.Colors.GREEN),
                        title=ft.Text(
                            "Biyometrik Giriş" if current_language == "tr"
                            else "الدخول البيومتري" if current_language == "ar"
                            else "Biometric Login"
                        ),
                        subtitle=ft.Text(
                            "Parmak izi veya yüz tanıma" if current_language == "tr"
                            else "بصمة الإصبع أو التعرف على الوجه" if current_language == "ar"
                            else "Fingerprint or face recognition"
                        ),
                        trailing=ft.Switch(value=True, on_change=toggle_biometric)
                    ),

                    ft.Divider(),

                    # قسم الجلسة
                    ft.Text(
                        "Oturum Yönetimi" if current_language == "tr"
                        else "إدارة الجلسة" if current_language == "ar"
                        else "Session Management",
                        size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.ORANGE_600
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.TIMER, color=ft.Colors.ORANGE),
                        title=ft.Text(
                            "Oturum Zaman Aşımı" if current_language == "tr"
                            else "انتهاء مهلة الجلسة" if current_language == "ar"
                            else "Session Timeout"
                        ),
                        subtitle=ft.Text(
                            "30 dakika sonra otomatik çıkış" if current_language == "tr"
                            else "خروج تلقائي بعد 30 دقيقة" if current_language == "ar"
                            else "Auto logout after 30 minutes"
                        ),
                        trailing=ft.Switch(value=True, on_change=toggle_session_timeout)
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.HISTORY, color=ft.Colors.BLUE),
                        title=ft.Text(
                            "Giriş Geçmişi" if current_language == "tr"
                            else "سجل تسجيل الدخول" if current_language == "ar"
                            else "Login History"
                        ),
                        subtitle=ft.Text(
                            "Son girişleri görüntüle" if current_language == "tr"
                            else "عرض آخر عمليات الدخول" if current_language == "ar"
                            else "View recent logins"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=show_login_history
                    ),

                    ft.Divider(),

                    # قسم الحساب
                    ft.Text(
                        "Hesap Güvenliği" if current_language == "tr"
                        else "أمان الحساب" if current_language == "ar"
                        else "Account Security",
                        size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.PURPLE_600
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.LOCK_RESET, color=ft.Colors.PURPLE),
                        title=ft.Text(
                            "Şifre Değiştir" if current_language == "tr"
                            else "تغيير كلمة المرور" if current_language == "ar"
                            else "Change Password"
                        ),
                        subtitle=ft.Text(
                            "Güçlü şifre oluştur" if current_language == "tr"
                            else "إنشاء كلمة مرور قوية" if current_language == "ar"
                            else "Create strong password"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=change_password
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.LOGOUT, color=ft.Colors.RED),
                        title=ft.Text(
                            "Tüm Cihazlardan Çıkış" if current_language == "tr"
                            else "تسجيل الخروج من جميع الأجهزة" if current_language == "ar"
                            else "Logout from All Devices"
                        ),
                        subtitle=ft.Text(
                            "Güvenlik için önerilen" if current_language == "tr"
                            else "موصى به للأمان" if current_language == "ar"
                            else "Recommended for security"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=logout_all_devices
                    ),

                    ft.Divider(),

                    # قسم إضافي - الخصوصية والأجهزة
                    ft.Text(
                        "Gizlilik ve Cihazlar" if current_language == "tr"
                        else "الخصوصية والأجهزة" if current_language == "ar"
                        else "Privacy & Devices",
                        size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.TEAL_600
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.PRIVACY_TIP, color=ft.Colors.TEAL),
                        title=ft.Text(
                            "Gizlilik Ayarları" if current_language == "tr"
                            else "إعدادات الخصوصية" if current_language == "ar"
                            else "Privacy Settings"
                        ),
                        subtitle=ft.Text(
                            "Veri paylaşımı ve gizlilik" if current_language == "tr"
                            else "مشاركة البيانات والخصوصية" if current_language == "ar"
                            else "Data sharing and privacy"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=show_privacy_settings
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.DEVICES, color=ft.Colors.INDIGO),
                        title=ft.Text(
                            "Cihaz Yönetimi" if current_language == "tr"
                            else "إدارة الأجهزة" if current_language == "ar"
                            else "Device Management"
                        ),
                        subtitle=ft.Text(
                            "Bağlı cihazları görüntüle" if current_language == "tr"
                            else "عرض الأجهزة المتصلة" if current_language == "ar"
                            else "View connected devices"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=show_device_management
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.BACKUP, color=ft.Colors.BROWN),
                        title=ft.Text(
                            "Yedekleme ve Geri Yükleme" if current_language == "tr"
                            else "النسخ الاحتياطي والاستعادة" if current_language == "ar"
                            else "Backup & Restore"
                        ),
                        subtitle=ft.Text(
                            "Verilerinizi yedekleyin" if current_language == "tr"
                            else "قم بنسخ بياناتك احتياطياً" if current_language == "ar"
                            else "Backup your data"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=lambda e: show_backup_message()
                    ),

                    ft.Container(height=20),

                    # مؤشر التمرير
                    ft.Container(
                        content=ft.Row([
                            ft.Icon(ft.Icons.SWIPE_VERTICAL, color=ft.Colors.GREY_400, size=16),
                            ft.Text(
                                "Kaydırarak daha fazla seçenek görün" if current_language == "tr"
                                else "مرر لأسفل لرؤية المزيد من الخيارات" if current_language == "ar"
                                else "Scroll to see more options",
                                size=12,
                                color=ft.Colors.GREY_500
                            )
                        ], alignment=ft.MainAxisAlignment.CENTER),
                        padding=10
                    ),

                    ft.Container(height=10),

                    # زر الرجوع
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.ARROW_BACK),
                            ft.Text(
                                "Geri Dön" if current_language == "tr"
                                else "العودة" if current_language == "ar"
                                else "Go Back"
                            )
                        ], alignment=ft.MainAxisAlignment.CENTER),
                        width=200,
                        on_click=lambda e: close_security()
                    ),

                    # مساحة إضافية في الأسفل
                    ft.Container(height=50)

                ], spacing=8, scroll=ft.ScrollMode.ALWAYS),
                padding=20,
                expand=True
            )
        )

    def show_language_menu(e):
        print("Language menu clicked!")  # للتحقق

        # استخدام صفحة جديدة للغات
        def close_language():
            page.clean()
            main(page)

        def select_language(lang):
            nonlocal current_language
            current_language = lang

            # رسالة تأكيد تغيير اللغة
            page.snack_bar = ft.SnackBar(content=ft.Text(
                f"Dil değiştirildi: {texts[lang]['language_name']}" if lang == "tr"
                else f"تم تغيير اللغة: {texts[lang]['language_name']}" if lang == "ar"
                else f"Language changed: {texts[lang]['language_name']}"
            ))
            page.snack_bar.open = True
            page.update()

            # العودة للصفحة الرئيسية بعد ثانيتين
            import time
            import threading
            def go_back():
                time.sleep(2)
                close_language()
            threading.Thread(target=go_back).start()

        # مسح الصفحة الحالية وإظهار صفحة اللغات
        page.clean()

        page.add(
            ft.Container(
                content=ft.Column([
                    # شريط علوي مع زر الرجوع
                    ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.ARROW_BACK,
                            on_click=lambda e: close_language()
                        ),
                        ft.Text(
                            "Dil Seçimi" if current_language == "tr"
                            else "اختيار اللغة" if current_language == "ar"
                            else "Language Selection",
                            size=20, weight=ft.FontWeight.BOLD
                        ),
                        ft.Container(expand=True),
                    ]),

                    ft.Divider(),

                    # أيقونة اللغة
                    ft.Icon(ft.Icons.LANGUAGE, size=60, color=ft.Colors.BLUE_600),

                    ft.Text(
                        "Tercih ettiğiniz dili seçin" if current_language == "tr"
                        else "اختر اللغة المفضلة لديك" if current_language == "ar"
                        else "Choose your preferred language",
                        text_align=ft.TextAlign.CENTER,
                        size=14,
                        color=ft.Colors.GREY_600
                    ),

                    ft.Container(height=20),

                    # اللغة التركية
                    ft.Container(
                        content=ft.ListTile(
                            leading=ft.Container(
                                content=ft.Text("🇹🇷", size=30),
                                width=50,
                                alignment=ft.alignment.center
                            ),
                            title=ft.Text(
                                "Türkçe",
                                size=18,
                                weight=ft.FontWeight.BOLD
                            ),
                            subtitle=ft.Text(
                                "Turkish • Türkiye",
                                size=12,
                                color=ft.Colors.GREY_600
                            ),
                            trailing=ft.Icon(
                                ft.Icons.CHECK_CIRCLE if current_language == "tr" else ft.Icons.RADIO_BUTTON_UNCHECKED,
                                color=ft.Colors.GREEN if current_language == "tr" else ft.Colors.GREY_400
                            ),
                            on_click=lambda e: select_language("tr")
                        ),
                        bgcolor=ft.Colors.GREEN_50 if current_language == "tr" else ft.Colors.TRANSPARENT,
                        border_radius=10,
                        border=ft.border.all(2, ft.Colors.GREEN_300) if current_language == "tr" else ft.border.all(1, ft.Colors.GREY_300),
                        padding=5
                    ),

                    ft.Container(height=10),

                    # اللغة العربية
                    ft.Container(
                        content=ft.ListTile(
                            leading=ft.Container(
                                content=ft.Text("🇸🇦", size=30),
                                width=50,
                                alignment=ft.alignment.center
                            ),
                            title=ft.Text(
                                "العربية",
                                size=18,
                                weight=ft.FontWeight.BOLD
                            ),
                            subtitle=ft.Text(
                                "Arabic • العربية",
                                size=12,
                                color=ft.Colors.GREY_600
                            ),
                            trailing=ft.Icon(
                                ft.Icons.CHECK_CIRCLE if current_language == "ar" else ft.Icons.RADIO_BUTTON_UNCHECKED,
                                color=ft.Colors.GREEN if current_language == "ar" else ft.Colors.GREY_400
                            ),
                            on_click=lambda e: select_language("ar")
                        ),
                        bgcolor=ft.Colors.GREEN_50 if current_language == "ar" else ft.Colors.TRANSPARENT,
                        border_radius=10,
                        border=ft.border.all(2, ft.Colors.GREEN_300) if current_language == "ar" else ft.border.all(1, ft.Colors.GREY_300),
                        padding=5
                    ),

                    ft.Container(height=10),

                    # اللغة الإنجليزية
                    ft.Container(
                        content=ft.ListTile(
                            leading=ft.Container(
                                content=ft.Text("🇺🇸", size=30),
                                width=50,
                                alignment=ft.alignment.center
                            ),
                            title=ft.Text(
                                "English",
                                size=18,
                                weight=ft.FontWeight.BOLD
                            ),
                            subtitle=ft.Text(
                                "English • United States",
                                size=12,
                                color=ft.Colors.GREY_600
                            ),
                            trailing=ft.Icon(
                                ft.Icons.CHECK_CIRCLE if current_language == "en" else ft.Icons.RADIO_BUTTON_UNCHECKED,
                                color=ft.Colors.GREEN if current_language == "en" else ft.Colors.GREY_400
                            ),
                            on_click=lambda e: select_language("en")
                        ),
                        bgcolor=ft.Colors.GREEN_50 if current_language == "en" else ft.Colors.TRANSPARENT,
                        border_radius=10,
                        border=ft.border.all(2, ft.Colors.GREEN_300) if current_language == "en" else ft.border.all(1, ft.Colors.GREY_300),
                        padding=5
                    ),

                    ft.Container(height=30),

                    # معلومات إضافية
                    ft.Container(
                        content=ft.Column([
                            ft.Text(
                                "ℹ️ Bilgi" if current_language == "tr"
                                else "ℹ️ معلومة" if current_language == "ar"
                                else "ℹ️ Information",
                                size=14,
                                weight=ft.FontWeight.BOLD,
                                color=ft.Colors.BLUE_700
                            ),
                            ft.Text(
                                "Dil değişikliği anında uygulanır ve uygulama yeniden başlatılır." if current_language == "tr"
                                else "يتم تطبيق تغيير اللغة فوراً وإعادة تشغيل التطبيق." if current_language == "ar"
                                else "Language change is applied instantly and the app restarts.",
                                size=12,
                                color=ft.Colors.GREY_600,
                                text_align=ft.TextAlign.CENTER
                            )
                        ], spacing=5),
                        padding=15,
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=10,
                        border=ft.border.all(1, ft.Colors.BLUE_200)
                    ),

                    ft.Container(height=20),

                    # زر الرجوع
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.ARROW_BACK),
                            ft.Text(
                                "Geri Dön" if current_language == "tr"
                                else "العودة" if current_language == "ar"
                                else "Go Back"
                            )
                        ], alignment=ft.MainAxisAlignment.CENTER),
                        width=200,
                        on_click=lambda e: close_language()
                    ),

                    # مساحة إضافية في الأسفل
                    ft.Container(height=30)

                ], spacing=10, scroll=ft.ScrollMode.AUTO),
                padding=20,
                expand=True
            )
        )

    def show_theme(e):
        print("Theme clicked!")  # للتحقق

        # استخدام صفحة جديدة للمظاهر
        def close_theme():
            page.clean()
            main(page)

        def change_theme(theme_mode, message):
            page.theme_mode = theme_mode
            page.snack_bar = ft.SnackBar(content=ft.Text(message))
            page.snack_bar.open = True
            page.update()
            # العودة للصفحة الرئيسية بعد ثانيتين
            import time
            import threading
            def go_back():
                time.sleep(2)
                close_theme()
            threading.Thread(target=go_back).start()

        def light_theme(e):
            change_theme(ft.ThemeMode.LIGHT,
                "Açık tema seçildi" if current_language == "tr"
                else "تم اختيار المظهر الفاتح" if current_language == "ar"
                else "Light theme selected")

        def dark_theme(e):
            change_theme(ft.ThemeMode.DARK,
                "Koyu tema seçildi" if current_language == "tr"
                else "تم اختيار المظهر الداكن" if current_language == "ar"
                else "Dark theme selected")

        def system_theme(e):
            change_theme(ft.ThemeMode.SYSTEM,
                "Sistem teması seçildi" if current_language == "tr"
                else "تم اختيار مظهر النظام" if current_language == "ar"
                else "System theme selected")

        # مسح الصفحة الحالية وإظهار صفحة المظاهر
        page.clean()

        page.add(
            ft.Container(
                content=ft.Column([
                    # شريط علوي مع زر الرجوع
                    ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.ARROW_BACK,
                            on_click=lambda e: close_theme()
                        ),
                        ft.Text(
                            "Tema Seçin" if current_language == "tr"
                            else "اختر المظهر" if current_language == "ar"
                            else "Choose Theme",
                            size=20, weight=ft.FontWeight.BOLD
                        ),
                        ft.Container(expand=True),
                    ]),

                    ft.Divider(),

                    # خيارات المظاهر
                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.LIGHT_MODE, color=ft.Colors.ORANGE, size=30),
                        title=ft.Text(
                            "Açık Tema" if current_language == "tr"
                            else "المظهر الفاتح" if current_language == "ar"
                            else "Light Theme"
                        ),
                        subtitle=ft.Text(
                            "Beyaz arka plan" if current_language == "tr"
                            else "خلفية بيضاء" if current_language == "ar"
                            else "White background"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=light_theme
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.DARK_MODE, color=ft.Colors.BLUE_GREY, size=30),
                        title=ft.Text(
                            "Koyu Tema" if current_language == "tr"
                            else "المظهر الداكن" if current_language == "ar"
                            else "Dark Theme"
                        ),
                        subtitle=ft.Text(
                            "Siyah arka plan" if current_language == "tr"
                            else "خلفية سوداء" if current_language == "ar"
                            else "Black background"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=dark_theme
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.SETTINGS_SYSTEM_DAYDREAM, color=ft.Colors.GREEN, size=30),
                        title=ft.Text(
                            "Sistem Teması" if current_language == "tr"
                            else "مظهر النظام" if current_language == "ar"
                            else "System Theme"
                        ),
                        subtitle=ft.Text(
                            "Sistem ayarlarını takip et" if current_language == "tr"
                            else "اتبع إعدادات النظام" if current_language == "ar"
                            else "Follow system settings"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=system_theme
                    ),

                    ft.Container(height=30),

                    # زر الرجوع
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.ARROW_BACK),
                            ft.Text(
                                "Geri Dön" if current_language == "tr"
                                else "العودة" if current_language == "ar"
                                else "Go Back"
                            )
                        ], alignment=ft.MainAxisAlignment.CENTER),
                        width=200,
                        on_click=lambda e: close_theme()
                    )

                ], spacing=15, scroll=ft.ScrollMode.AUTO),
                padding=20,
                expand=True
            )
        )

    def show_privacy(e):
        page.snack_bar = ft.SnackBar(content=ft.Text(texts[current_language]["privacy_text"]))
        page.snack_bar.open = True
        page.update()

    def login_clicked(e):
        # Veri doğrulama
        if not email_field.value:
            email_field.error_text = texts[current_language]["email_error"]
            page.update()
            return

        if not password_field.value:
            password_field.error_text = texts[current_language]["password_error"]
            page.update()
            return

        # Hata mesajlarını temizle
        email_field.error_text = ""
        password_field.error_text = ""

        # Başarılı giriş mesajı
        success_message.value = f"{texts[current_language]['success_message']}{email_field.value}"
        success_message.visible = True
        login_button.disabled = True
        page.update()

    def register_clicked(e):
        # Kayıt mantığı buraya eklenebilir
        page.snack_bar = ft.SnackBar(content=ft.Text(texts[current_language]["register_text"]))
        page.snack_bar.open = True
        page.update()

    def forgot_password_clicked(e):
        page.snack_bar = ft.SnackBar(content=ft.Text(texts[current_language]["forgot_text"]))
        page.snack_bar.open = True
        page.update()

    # Giriş alanları - responsive
    email_field = ft.TextField(
        label=texts[current_language]["email_label"],
        hint_text=texts[current_language]["email_hint"],
        expand=True,  # Mevcut alanı doldur
        prefix_icon=ft.Icons.EMAIL,
        keyboard_type=ft.KeyboardType.EMAIL,
        autofocus=True
    )

    password_field = ft.TextField(
        label=texts[current_language]["password_label"],
        hint_text=texts[current_language]["password_hint"],
        expand=True,  # Mevcut alanı doldur
        prefix_icon=ft.Icons.LOCK,
        password=True,
        can_reveal_password=True
    )

    # Uygulama düğmeleri - responsive
    login_button = ft.ElevatedButton(
        text=texts[current_language]["login_button"],
        expand=True,  # Mevcut alanı doldur
        height=45,
        on_click=login_clicked,
        style=ft.ButtonStyle(
            color=ft.Colors.WHITE,
            bgcolor=ft.Colors.BLUE_600,
        )
    )

    register_button = ft.TextButton(
        text=texts[current_language]["register_button"],
        on_click=register_clicked
    )

    forgot_password_button = ft.TextButton(
        text=texts[current_language]["forgot_button"],
        on_click=forgot_password_clicked
    )

    # رسالة النجاح
    success_message = ft.Text(
        value="",
        color=ft.Colors.GREEN_600,
        size=16,
        text_align=ft.TextAlign.CENTER,
        visible=False
    )



    # تخطيط الصفحة - responsive
    page.add(
        ft.Container(
            content=ft.Column([
                # قائمة الإعدادات في الأعلى
                ft.Row([
                    ft.Container(expand=True),  # مساحة فارغة لدفع القائمة لليمين
                    ft.PopupMenuButton(
                        icon=ft.Icons.MORE_VERT,  # أيقونة الثلاث نقاط
                        items=[
                            ft.PopupMenuItem(
                                text=texts[current_language]["about"],
                                icon=ft.Icons.INFO,
                                on_click=show_about
                            ),
                            ft.PopupMenuItem(
                                text=texts[current_language]["help"],
                                icon=ft.Icons.HELP,
                                on_click=show_help
                            ),
                            ft.PopupMenuItem(
                                text=texts[current_language]["settings"],
                                icon=ft.Icons.SETTINGS,
                                on_click=show_settings
                            ),
                            ft.PopupMenuItem(),  # Ayırıcı çizgi
                            ft.PopupMenuItem(
                                text=texts[current_language]["language"],
                                icon=ft.Icons.LANGUAGE,
                                on_click=show_language_menu
                            ),
                            ft.PopupMenuItem(
                                text=texts[current_language]["theme"],
                                icon=ft.Icons.PALETTE,
                                on_click=show_theme
                            ),
                            ft.PopupMenuItem(),  # Ayırıcı çizgi
                            ft.PopupMenuItem(
                                text=texts[current_language]["privacy"],
                                icon=ft.Icons.PRIVACY_TIP,
                                on_click=show_privacy
                            ),
                        ]
                    )
                ], alignment=ft.MainAxisAlignment.END),

                # العنوان والشعار - responsive
                ft.Text(
                    "🛡️",
                    size=60,  # حجم كبير للوجو
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    "Mürşid",
                    size=24,  # حجم كبير لاسم التطبيق
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_600,
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    texts[current_language]["login_title"],
                    size=18,  # Alt başlık için küçük boyut
                    weight=ft.FontWeight.W_500,
                    color=ft.Colors.BLUE_700,
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    texts[current_language]["welcome"],
                    size=14,  # Orta boyut
                    color=ft.Colors.GREY_600,
                    text_align=ft.TextAlign.CENTER
                ),

                # مساحة فارغة
                ft.Container(height=10),  # مساحة صغيرة جداً

                # حقول الإدخال - responsive
                ft.ResponsiveRow([
                    ft.Column([
                        email_field,
                        ft.Container(height=8),
                        password_field,
                    ], col={"sm": 12, "md": 10, "lg": 8}, alignment=ft.MainAxisAlignment.CENTER)
                ], alignment=ft.MainAxisAlignment.CENTER),

                # مساحة فارغة
                ft.Container(height=10),  # مساحة صغيرة جداً

                # زر تسجيل الدخول - responsive
                ft.ResponsiveRow([
                    ft.Column([
                        login_button,
                    ], col={"sm": 12, "md": 10, "lg": 8}, alignment=ft.MainAxisAlignment.CENTER)
                ], alignment=ft.MainAxisAlignment.CENTER),

                # رسالة النجاح
                success_message,

                # مساحة فارغة
                ft.Container(height=8),  # مساحة أصغر للهاتف

                # أزرار إضافية
                register_button,
                forgot_password_button,

            ],
            alignment=ft.MainAxisAlignment.CENTER,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=8,  # مساحة متكيفة
            tight=True  # يتكيف مع المحتوى
            ),
            padding=ft.padding.symmetric(horizontal=20, vertical=20),  # حشو متكيف
            margin=ft.margin.only(left=10, right=10, top=10, bottom=10),  # هامش خارجي
            border_radius=10,
            bgcolor=ft.Colors.WHITE,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=15,
                color=ft.Colors.BLUE_GREY_300,
                offset=ft.Offset(0, 0),
            ),
            expand=True,  # يتوسع ليملأ المساحة المتاحة
            width=None,  # عرض متكيف
            height=None  # ارتفاع متكيف
        )
    )

if __name__ == "__main__":
    ft.app(target=main)
