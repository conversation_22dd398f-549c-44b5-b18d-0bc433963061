#!/usr/bin/env python3
"""
Mürşid Giriş Uygulaması - Mürşid Login Application
"""

import flet as ft

def main(page: ft.Page):
    page.title = "<PERSON>iriş Yap - Mürşid"
    page.vertical_alignment = ft.MainAxisAlignment.CENTER
    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
    page.window_width = 350  # Varsayılan boyut
    page.window_height = 500  # Varsayılan boyut
    page.window_resizable = True  # Boyut değiştirilebilir
    page.window_min_width = 280  # Minimum genişlik
    page.window_min_height = 400  # Minimum yükseklik
    page.padding = 10  # Sayfa dolgusu

    # Kullanıcı verilerini kaydetmek için deği<PERSON>kenler
    if not hasattr(page, 'current_language'):
        page.current_language = "tr"  # Varsayılan dil: Türkçe
    current_language = page.current_language

    # Dil metinleri
    texts = {
        "tr": {
            "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            "login_title": "<PERSON><PERSON><PERSON> Ya<PERSON>",
            "welcome": "<PERSON><PERSON><PERSON><PERSON><PERSON> uygulamasına ho<PERSON> geldiniz",
            "email_label": "E-posta",
            "email_hint": "<EMAIL>",
            "password_label": "Şifre",
            "password_hint": "Şifrenizi girin",
            "login_button": "Giriş Yap",
            "register_button": "Yeni hesap oluştur",
            "forgot_button": "Şifremi unuttum",
            "email_error": "Lütfen e-posta adresinizi girin",
            "password_error": "Lütfen şifrenizi girin",
            "success_message": "Hoş geldiniz! Giriş başarılı\nE-posta: ",
            "about": "Hakkında",
            "help": "Yardım",
            "settings": "Ayarlar",
            "language": "Dil",
            "theme": "Tema",
            "privacy": "Gizlilik",
            "about_text": "Mürşid v1.0 - Güvenli Giriş Uygulaması",
            "help_text": "Yardım: E-posta ve şifrenizi girin",
            "settings_text": "Ayarlar sayfası yakında eklenecek",
            "theme_text": "Tema değiştirme yakında eklenecek",
            "privacy_text": "Gizlilik Politikası yakında eklenecek",
            "register_text": "Kayıt sayfası yakında eklenecek",
            "forgot_text": "Şifre sıfırlama yakında eklenecek"
        },
        "ar": {
            "title": "تسجيل الدخول - Mürşid",
            "login_title": "تسجيل الدخول",
            "welcome": "مرحباً بك في تطبيق Mürşid",
            "email_label": "البريد الإلكتروني",
            "email_hint": "<EMAIL>",
            "password_label": "كلمة المرور",
            "password_hint": "أدخل كلمة المرور",
            "login_button": "تسجيل الدخول",
            "register_button": "إنشاء حساب جديد",
            "forgot_button": "نسيت كلمة المرور؟",
            "email_error": "يرجى إدخال البريد الإلكتروني",
            "password_error": "يرجى إدخال كلمة المرور",
            "success_message": "مرحباً! تم تسجيل الدخول بنجاح\nالبريد الإلكتروني: ",
            "about": "حول التطبيق",
            "help": "المساعدة",
            "settings": "الإعدادات",
            "language": "اللغة",
            "theme": "المظهر",
            "privacy": "الخصوصية",
            "about_text": "Mürşid الإصدار 1.0 - تطبيق دخول آمن",
            "help_text": "المساعدة: أدخل بريدك الإلكتروني وكلمة المرور",
            "settings_text": "صفحة الإعدادات ستضاف قريباً",
            "theme_text": "تغيير المظهر سيضاف قريباً",
            "privacy_text": "سياسة الخصوصية ستضاف قريباً",
            "register_text": "صفحة التسجيل ستضاف قريباً",
            "forgot_text": "استعادة كلمة المرور ستضاف قريباً"
        },
        "en": {
            "title": "Login - Mürşid",
            "login_title": "Login",
            "welcome": "Welcome to Mürşid application",
            "email_label": "Email",
            "email_hint": "<EMAIL>",
            "password_label": "Password",
            "password_hint": "Enter your password",
            "login_button": "Login",
            "register_button": "Create new account",
            "forgot_button": "Forgot password?",
            "email_error": "Please enter your email",
            "password_error": "Please enter your password",
            "success_message": "Welcome! Login successful\nEmail: ",
            "about": "About",
            "help": "Help",
            "settings": "Settings",
            "language": "Language",
            "theme": "Theme",
            "privacy": "Privacy",
            "about_text": "Mürşid v1.0 - Secure Login Application",
            "help_text": "Help: Enter your email and password",
            "settings_text": "Settings page coming soon",
            "theme_text": "Theme changing coming soon",
            "privacy_text": "Privacy Policy coming soon",
            "register_text": "Registration page coming soon",
            "forgot_text": "Password reset coming soon"
        }
    }

    # Sayfa başlığını ayarla
    page.title = texts[current_language]["title"]

    # Dil seçimi fonksiyonları
    def change_to_turkish(e):
        if hasattr(page, 'dialog') and page.dialog:
            page.dialog.open = False
        page.current_language = "tr"
        page.snack_bar = ft.SnackBar(content=ft.Text("Dil Türkçe olarak değiştirildi"))
        page.snack_bar.open = True
        page.update()
        # Sayfayı yeniden yükle
        import time
        time.sleep(0.5)
        page.clean()
        main(page)

    def change_to_arabic(e):
        if hasattr(page, 'dialog') and page.dialog:
            page.dialog.open = False
        page.current_language = "ar"
        page.snack_bar = ft.SnackBar(content=ft.Text("تم تغيير اللغة إلى العربية"))
        page.snack_bar.open = True
        page.update()
        # Sayfayı yeniden yükle
        import time
        time.sleep(0.5)
        page.clean()
        main(page)

    def change_to_english(e):
        if hasattr(page, 'dialog') and page.dialog:
            page.dialog.open = False
        page.current_language = "en"
        page.snack_bar = ft.SnackBar(content=ft.Text("Language changed to English"))
        page.snack_bar.open = True
        page.update()
        # Sayfayı yeniden yükle
        import time
        time.sleep(0.5)
        page.clean()
        main(page)

    # Ayarlar menüsü fonksiyonları
    def show_about(e):
        page.snack_bar = ft.SnackBar(content=ft.Text(texts[current_language]["about_text"]))
        page.snack_bar.open = True
        page.update()

    def show_help(e):
        page.snack_bar = ft.SnackBar(content=ft.Text(texts[current_language]["help_text"]))
        page.snack_bar.open = True
        page.update()

    def show_settings(e):
        # إعدادات التطبيق
        def close_settings_dialog(e):
            settings_dialog.open = False
            page.update()

        def toggle_notifications(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Bildirimler açıldı/kapatıldı" if current_language == "tr"
                else "تم تشغيل/إيقاف الإشعارات" if current_language == "ar"
                else "Notifications turned on/off"
            ))
            page.snack_bar.open = True
            page.update()

        def toggle_auto_login(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Otomatik giriş açıldı/kapatıldı" if current_language == "tr"
                else "تم تشغيل/إيقاف الدخول التلقائي" if current_language == "ar"
                else "Auto-login turned on/off"
            ))
            page.snack_bar.open = True
            page.update()

        def toggle_remember_me(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Beni hatırla açıldı/kapatıldı" if current_language == "tr"
                else "تم تشغيل/إيقاف تذكرني" if current_language == "ar"
                else "Remember me turned on/off"
            ))
            page.snack_bar.open = True
            page.update()

        def clear_data(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Veriler temizlendi" if current_language == "tr"
                else "تم مسح البيانات" if current_language == "ar"
                else "Data cleared"
            ))
            page.snack_bar.open = True
            page.update()

        settings_dialog = ft.AlertDialog(
            title=ft.Text(
                "Ayarlar" if current_language == "tr"
                else "الإعدادات" if current_language == "ar"
                else "Settings"
            ),
            content=ft.Column([
                # إعدادات الإشعارات
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.NOTIFICATIONS),
                    title=ft.Text(
                        "Bildirimler" if current_language == "tr"
                        else "الإشعارات" if current_language == "ar"
                        else "Notifications"
                    ),
                    trailing=ft.Switch(value=True, on_change=toggle_notifications)
                ),
                ft.Divider(),

                # إعدادات الدخول التلقائي
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.LOGIN),
                    title=ft.Text(
                        "Otomatik Giriş" if current_language == "tr"
                        else "الدخول التلقائي" if current_language == "ar"
                        else "Auto Login"
                    ),
                    trailing=ft.Switch(value=False, on_change=toggle_auto_login)
                ),
                ft.Divider(),

                # إعدادات تذكرني
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.MEMORY),
                    title=ft.Text(
                        "Beni Hatırla" if current_language == "tr"
                        else "تذكرني" if current_language == "ar"
                        else "Remember Me"
                    ),
                    trailing=ft.Switch(value=True, on_change=toggle_remember_me)
                ),
                ft.Divider(),

                # مسح البيانات
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.DELETE_FOREVER, color=ft.Colors.RED),
                    title=ft.Text(
                        "Verileri Temizle" if current_language == "tr"
                        else "مسح البيانات" if current_language == "ar"
                        else "Clear Data",
                        color=ft.Colors.RED
                    ),
                    on_click=clear_data
                ),
            ], tight=True, scroll=ft.ScrollMode.AUTO),
            actions=[
                ft.TextButton(
                    "Kapat" if current_language == "tr"
                    else "إغلاق" if current_language == "ar"
                    else "Close",
                    on_click=close_settings_dialog
                )
            ]
        )

        page.dialog = settings_dialog
        settings_dialog.open = True
        page.update()

    def show_language_menu(e):
        # Dil seçimi dialog göster
        def close_dialog(e):
            dialog.open = False
            page.update()

        dialog = ft.AlertDialog(
            title=ft.Text("Dil Seçin / اختر اللغة / Choose Language"),
            content=ft.Column([
                ft.ElevatedButton(
                    content=ft.Row([
                        ft.Text("🇹🇷", size=20),
                        ft.Text("Türkçe", size=16)
                    ], alignment=ft.MainAxisAlignment.START),
                    width=200,
                    on_click=change_to_turkish
                ),
                ft.ElevatedButton(
                    content=ft.Row([
                        ft.Text("🇸🇦", size=20),
                        ft.Text("العربية", size=16)
                    ], alignment=ft.MainAxisAlignment.START),
                    width=200,
                    on_click=change_to_arabic
                ),
                ft.ElevatedButton(
                    content=ft.Row([
                        ft.Text("🇺🇸", size=20),
                        ft.Text("English", size=16)
                    ], alignment=ft.MainAxisAlignment.START),
                    width=200,
                    on_click=change_to_english
                ),
            ], tight=True),
            actions=[
                ft.TextButton("İptal / إلغاء / Cancel", on_click=close_dialog)
            ]
        )

        page.dialog = dialog
        dialog.open = True
        page.update()

    def show_theme(e):
        # إعدادات المظهر
        def close_theme_dialog(e):
            theme_dialog.open = False
            page.update()

        def change_to_light_theme(e):
            page.theme_mode = ft.ThemeMode.LIGHT
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Açık tema seçildi" if current_language == "tr"
                else "تم اختيار المظهر الفاتح" if current_language == "ar"
                else "Light theme selected"
            ))
            page.snack_bar.open = True
            page.update()
            theme_dialog.open = False
            page.update()

        def change_to_dark_theme(e):
            page.theme_mode = ft.ThemeMode.DARK
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Koyu tema seçildi" if current_language == "tr"
                else "تم اختيار المظهر الداكن" if current_language == "ar"
                else "Dark theme selected"
            ))
            page.snack_bar.open = True
            page.update()
            theme_dialog.open = False
            page.update()

        def change_to_system_theme(e):
            page.theme_mode = ft.ThemeMode.SYSTEM
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Sistem teması seçildi" if current_language == "tr"
                else "تم اختيار مظهر النظام" if current_language == "ar"
                else "System theme selected"
            ))
            page.snack_bar.open = True
            page.update()
            theme_dialog.open = False
            page.update()

        theme_dialog = ft.AlertDialog(
            title=ft.Text(
                "Tema Seçin" if current_language == "tr"
                else "اختر المظهر" if current_language == "ar"
                else "Choose Theme"
            ),
            content=ft.Column([
                # المظهر الفاتح
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.LIGHT_MODE, color=ft.Colors.ORANGE),
                    title=ft.Text(
                        "Açık Tema" if current_language == "tr"
                        else "المظهر الفاتح" if current_language == "ar"
                        else "Light Theme"
                    ),
                    subtitle=ft.Text(
                        "Beyaz arka plan" if current_language == "tr"
                        else "خلفية بيضاء" if current_language == "ar"
                        else "White background"
                    ),
                    on_click=change_to_light_theme
                ),
                ft.Divider(),

                # المظهر الداكن
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.DARK_MODE, color=ft.Colors.BLUE_GREY),
                    title=ft.Text(
                        "Koyu Tema" if current_language == "tr"
                        else "المظهر الداكن" if current_language == "ar"
                        else "Dark Theme"
                    ),
                    subtitle=ft.Text(
                        "Siyah arka plan" if current_language == "tr"
                        else "خلفية سوداء" if current_language == "ar"
                        else "Black background"
                    ),
                    on_click=change_to_dark_theme
                ),
                ft.Divider(),

                # مظهر النظام
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.SETTINGS_SYSTEM_DAYDREAM, color=ft.Colors.GREEN),
                    title=ft.Text(
                        "Sistem Teması" if current_language == "tr"
                        else "مظهر النظام" if current_language == "ar"
                        else "System Theme"
                    ),
                    subtitle=ft.Text(
                        "Sistem ayarlarını takip et" if current_language == "tr"
                        else "اتبع إعدادات النظام" if current_language == "ar"
                        else "Follow system settings"
                    ),
                    on_click=change_to_system_theme
                ),
            ], tight=True),
            actions=[
                ft.TextButton(
                    "İptal" if current_language == "tr"
                    else "إلغاء" if current_language == "ar"
                    else "Cancel",
                    on_click=close_theme_dialog
                )
            ]
        )

        page.dialog = theme_dialog
        theme_dialog.open = True
        page.update()

    def show_privacy(e):
        page.snack_bar = ft.SnackBar(content=ft.Text(texts[current_language]["privacy_text"]))
        page.snack_bar.open = True
        page.update()

    def login_clicked(e):
        # Veri doğrulama
        if not email_field.value:
            email_field.error_text = texts[current_language]["email_error"]
            page.update()
            return

        if not password_field.value:
            password_field.error_text = texts[current_language]["password_error"]
            page.update()
            return

        # Hata mesajlarını temizle
        email_field.error_text = ""
        password_field.error_text = ""

        # Başarılı giriş mesajı
        success_message.value = f"{texts[current_language]['success_message']}{email_field.value}"
        success_message.visible = True
        login_button.disabled = True
        page.update()

    def register_clicked(e):
        # Kayıt mantığı buraya eklenebilir
        page.snack_bar = ft.SnackBar(content=ft.Text(texts[current_language]["register_text"]))
        page.snack_bar.open = True
        page.update()

    def forgot_password_clicked(e):
        page.snack_bar = ft.SnackBar(content=ft.Text(texts[current_language]["forgot_text"]))
        page.snack_bar.open = True
        page.update()

    # Giriş alanları - responsive
    email_field = ft.TextField(
        label=texts[current_language]["email_label"],
        hint_text=texts[current_language]["email_hint"],
        expand=True,  # Mevcut alanı doldur
        prefix_icon=ft.Icons.EMAIL,
        keyboard_type=ft.KeyboardType.EMAIL,
        autofocus=True
    )

    password_field = ft.TextField(
        label=texts[current_language]["password_label"],
        hint_text=texts[current_language]["password_hint"],
        expand=True,  # Mevcut alanı doldur
        prefix_icon=ft.Icons.LOCK,
        password=True,
        can_reveal_password=True
    )

    # Uygulama düğmeleri - responsive
    login_button = ft.ElevatedButton(
        text=texts[current_language]["login_button"],
        expand=True,  # Mevcut alanı doldur
        height=45,
        on_click=login_clicked,
        style=ft.ButtonStyle(
            color=ft.Colors.WHITE,
            bgcolor=ft.Colors.BLUE_600,
        )
    )

    register_button = ft.TextButton(
        text=texts[current_language]["register_button"],
        on_click=register_clicked
    )

    forgot_password_button = ft.TextButton(
        text=texts[current_language]["forgot_button"],
        on_click=forgot_password_clicked
    )

    # رسالة النجاح
    success_message = ft.Text(
        value="",
        color=ft.Colors.GREEN_600,
        size=16,
        text_align=ft.TextAlign.CENTER,
        visible=False
    )



    # تخطيط الصفحة - responsive
    page.add(
        ft.Container(
            content=ft.Column([
                # قائمة الإعدادات في الأعلى
                ft.Row([
                    ft.Container(expand=True),  # مساحة فارغة لدفع القائمة لليمين
                    ft.PopupMenuButton(
                        icon=ft.Icons.MORE_VERT,  # أيقونة الثلاث نقاط
                        items=[
                            ft.PopupMenuItem(
                                text=texts[current_language]["about"],
                                icon=ft.Icons.INFO,
                                on_click=show_about
                            ),
                            ft.PopupMenuItem(
                                text=texts[current_language]["help"],
                                icon=ft.Icons.HELP,
                                on_click=show_help
                            ),
                            ft.PopupMenuItem(
                                text=texts[current_language]["settings"],
                                icon=ft.Icons.SETTINGS,
                                on_click=show_settings
                            ),
                            ft.PopupMenuItem(),  # Ayırıcı çizgi
                            ft.PopupMenuItem(
                                text=texts[current_language]["language"],
                                icon=ft.Icons.LANGUAGE,
                                on_click=show_language_menu
                            ),
                            ft.PopupMenuItem(
                                text=texts[current_language]["theme"],
                                icon=ft.Icons.PALETTE,
                                on_click=show_theme
                            ),
                            ft.PopupMenuItem(),  # Ayırıcı çizgi
                            ft.PopupMenuItem(
                                text=texts[current_language]["privacy"],
                                icon=ft.Icons.PRIVACY_TIP,
                                on_click=show_privacy
                            ),
                        ]
                    )
                ], alignment=ft.MainAxisAlignment.END),

                # العنوان والشعار - responsive
                ft.Text(
                    "🛡️",
                    size=60,  # حجم كبير للوجو
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    "Mürşid",
                    size=24,  # حجم كبير لاسم التطبيق
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_600,
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    texts[current_language]["login_title"],
                    size=18,  # Alt başlık için küçük boyut
                    weight=ft.FontWeight.W_500,
                    color=ft.Colors.BLUE_700,
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    texts[current_language]["welcome"],
                    size=14,  # Orta boyut
                    color=ft.Colors.GREY_600,
                    text_align=ft.TextAlign.CENTER
                ),

                # مساحة فارغة
                ft.Container(height=10),  # مساحة صغيرة جداً

                # حقول الإدخال - responsive
                ft.ResponsiveRow([
                    ft.Column([
                        email_field,
                        ft.Container(height=8),
                        password_field,
                    ], col={"sm": 12, "md": 10, "lg": 8}, alignment=ft.MainAxisAlignment.CENTER)
                ], alignment=ft.MainAxisAlignment.CENTER),

                # مساحة فارغة
                ft.Container(height=10),  # مساحة صغيرة جداً

                # زر تسجيل الدخول - responsive
                ft.ResponsiveRow([
                    ft.Column([
                        login_button,
                    ], col={"sm": 12, "md": 10, "lg": 8}, alignment=ft.MainAxisAlignment.CENTER)
                ], alignment=ft.MainAxisAlignment.CENTER),

                # رسالة النجاح
                success_message,

                # مساحة فارغة
                ft.Container(height=8),  # مساحة أصغر للهاتف

                # أزرار إضافية
                register_button,
                forgot_password_button,

            ],
            alignment=ft.MainAxisAlignment.CENTER,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=8,  # مساحة متكيفة
            tight=True  # يتكيف مع المحتوى
            ),
            padding=ft.padding.symmetric(horizontal=20, vertical=20),  # حشو متكيف
            margin=ft.margin.only(left=10, right=10, top=10, bottom=10),  # هامش خارجي
            border_radius=10,
            bgcolor=ft.Colors.WHITE,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=15,
                color=ft.Colors.BLUE_GREY_300,
                offset=ft.Offset(0, 0),
            ),
            expand=True,  # يتوسع ليملأ المساحة المتاحة
            width=None,  # عرض متكيف
            height=None  # ارتفاع متكيف
        )
    )

if __name__ == "__main__":
    ft.app(target=main)
