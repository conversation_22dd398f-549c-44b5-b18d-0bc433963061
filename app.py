#!/usr/bin/env python3
"""
Flet Login Application - تطبيق تسجيل الدخول
"""

import flet as ft

def main(page: ft.Page):
    page.title = "تسجيل الدخول - Zem Login"
    page.vertical_alignment = ft.MainAxisAlignment.CENTER
    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
    page.window_width = 400
    page.window_height = 500
    page.window_resizable = False

    # متغيرات لحفظ بيانات المستخدم
    def login_clicked(e):
        # التحقق من صحة البيانات
        if not email_field.value:
            email_field.error_text = "يرجى إدخال الإيميل"
            page.update()
            return

        if not password_field.value:
            password_field.error_text = "يرجى إدخال كلمة المرور"
            page.update()
            return

        # مسح رسائل الخطأ
        email_field.error_text = ""
        password_field.error_text = ""

        # عرض رسالة نجاح تسجيل الدخول
        success_message.value = f"مرحباً! تم تسجيل الدخول بنجاح\nالإيميل: {email_field.value}"
        success_message.visible = True
        login_button.disabled = True
        page.update()

    def register_clicked(e):
        # يمكن إضافة منطق التسجيل هنا
        page.show_snack_bar(
            ft.SnackBar(content=ft.Text("سيتم إضافة صفحة التسجيل قريباً"))
        )

    def forgot_password_clicked(e):
        page.show_snack_bar(
            ft.SnackBar(content=ft.Text("سيتم إضافة استعادة كلمة المرور قريباً"))
        )

    # حقول الإدخال
    email_field = ft.TextField(
        label="الإيميل / Email",
        hint_text="<EMAIL>",
        width=300,
        prefix_icon=ft.Icons.EMAIL,
        keyboard_type=ft.KeyboardType.EMAIL,
        autofocus=True
    )

    password_field = ft.TextField(
        label="كلمة المرور / Password",
        hint_text="أدخل كلمة المرور",
        width=300,
        prefix_icon=ft.Icons.LOCK,
        password=True,
        can_reveal_password=True
    )

    # أزرار التطبيق
    login_button = ft.ElevatedButton(
        text="تسجيل الدخول",
        width=300,
        height=45,
        on_click=login_clicked,
        style=ft.ButtonStyle(
            color=ft.Colors.WHITE,
            bgcolor=ft.Colors.BLUE_600,
        )
    )

    register_button = ft.TextButton(
        text="إنشاء حساب جديد",
        on_click=register_clicked
    )

    forgot_password_button = ft.TextButton(
        text="نسيت كلمة المرور؟",
        on_click=forgot_password_clicked
    )

    # رسالة النجاح
    success_message = ft.Text(
        value="",
        color=ft.Colors.GREEN_600,
        size=16,
        text_align=ft.TextAlign.CENTER,
        visible=False
    )

    # تخطيط الصفحة
    page.add(
        ft.Container(
            content=ft.Column([
                # العنوان والشعار
                ft.Icon(
                    name=ft.Icons.ACCOUNT_CIRCLE,
                    size=80,
                    color=ft.Colors.BLUE_600
                ),
                ft.Text(
                    "تسجيل الدخول",
                    size=28,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_600,
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    "مرحباً بك في تطبيق Zem",
                    size=16,
                    color=ft.Colors.GREY_600,
                    text_align=ft.TextAlign.CENTER
                ),

                # مساحة فارغة
                ft.Container(height=20),

                # حقول الإدخال
                email_field,
                ft.Container(height=10),
                password_field,

                # مساحة فارغة
                ft.Container(height=20),

                # زر تسجيل الدخول
                login_button,

                # رسالة النجاح
                success_message,

                # مساحة فارغة
                ft.Container(height=10),

                # أزرار إضافية
                register_button,
                forgot_password_button,

            ],
            alignment=ft.MainAxisAlignment.CENTER,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=5
            ),
            padding=ft.padding.all(30),
            border_radius=10,
            bgcolor=ft.Colors.WHITE,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=15,
                color=ft.Colors.BLUE_GREY_300,
                offset=ft.Offset(0, 0),
            )
        )
    )

if __name__ == "__main__":
    ft.app(target=main)
